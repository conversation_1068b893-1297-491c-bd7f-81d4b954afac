// Global variables
let selectedServices = [];
let currentDiscount = 0;

// Initialize the application
document.addEventListener('DOMContentLoaded', function() {
    initializeApp();
    setupEventListeners();
    updateCalculator();
});

function initializeApp() {
    // Set today's date
    const today = new Date();
    const todayElement = document.getElementById('todayDate');
    if (todayElement) {
        todayElement.textContent = today.toLocaleDateString('sk-SK');
    }
    
    // Initialize tabs
    showTab('quotes');
    
    // Initialize service navigation
    showServiceCategory('basic-services');
}

function setupEventListeners() {
    // Tab navigation
    document.querySelectorAll('.nav-tab').forEach(tab => {
        tab.addEventListener('click', function() {
            const tabName = this.getAttribute('data-tab');
            showTab(tabName);
        });
    });
    
    // Service navigation
    document.querySelectorAll('.service-nav .nav-link').forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            const categoryId = this.getAttribute('href').substring(1);
            showServiceCategory(categoryId);
        });
    });
    
    // Service checkboxes
    document.querySelectorAll('input[type="checkbox"][data-service]').forEach(checkbox => {
        checkbox.addEventListener('change', function() {
            updateSelectedServices();
            updateCalculator();
        });
    });
    
    // Custom input fields
    document.querySelectorAll('.custom-input input').forEach(input => {
        input.addEventListener('input', function() {
            updateSelectedServices();
            updateCalculator();
        });
    });
    
    // Discount options
    document.querySelectorAll('input[name="discount"]').forEach(radio => {
        radio.addEventListener('change', function() {
            currentDiscount = parseInt(this.value);
            updateCalculator();
        });
    });
    
    // PDF generation button
    const generateBtn = document.querySelector('.btn-generate');
    if (generateBtn) {
        generateBtn.addEventListener('click', generatePDF);
    }
}

function showTab(tabName) {
    // Hide all tab panes
    document.querySelectorAll('.tab-pane').forEach(pane => {
        pane.classList.remove('active');
    });
    
    // Remove active class from all nav tabs
    document.querySelectorAll('.nav-tab').forEach(tab => {
        tab.classList.remove('active');
    });
    
    // Show selected tab pane
    const selectedPane = document.getElementById(tabName + '-tab');
    if (selectedPane) {
        selectedPane.classList.add('active');
    }
    
    // Add active class to selected nav tab
    const selectedTab = document.querySelector(`[data-tab="${tabName}"]`);
    if (selectedTab) {
        selectedTab.classList.add('active');
    }
}

function showServiceCategory(categoryId) {
    // Hide all service categories
    document.querySelectorAll('.service-category').forEach(category => {
        category.classList.remove('active');
    });
    
    // Remove active class from all nav links
    document.querySelectorAll('.service-nav .nav-link').forEach(link => {
        link.classList.remove('active');
    });
    
    // Show selected category
    const selectedCategory = document.getElementById(categoryId);
    if (selectedCategory) {
        selectedCategory.classList.add('active');
    }
    
    // Add active class to selected nav link
    const selectedLink = document.querySelector(`[href="#${categoryId}"]`);
    if (selectedLink) {
        selectedLink.classList.add('active');
    }
}

function updateSelectedServices() {
    selectedServices = [];
    
    document.querySelectorAll('input[type="checkbox"][data-service]:checked').forEach(checkbox => {
        const serviceName = checkbox.getAttribute('data-service');
        let price = parseFloat(checkbox.getAttribute('data-price'));
        
        // Handle custom inputs
        const serviceItem = checkbox.closest('.service-item');
        const customInput = serviceItem.querySelector('.custom-input input');
        
        if (customInput && customInput.value) {
            const multiplier = customInput.getAttribute('data-multiplier');
            const additional = customInput.getAttribute('data-additional');
            
            if (multiplier) {
                price = parseFloat(customInput.value) * parseFloat(multiplier);
            } else if (additional) {
                price = parseFloat(customInput.value) + parseFloat(additional);
            }
        }
        
        selectedServices.push({
            name: serviceName,
            price: price
        });
    });
}

function updateCalculator() {
    const servicesList = document.getElementById('selectedServicesList');
    const subtotalElement = document.getElementById('subtotal');
    const discountRow = document.getElementById('discountRow');
    const discountPercent = document.getElementById('discountPercent');
    const discountAmount = document.getElementById('discountAmount');
    const subtotalAfterDiscount = document.getElementById('subtotalAfterDiscount');
    const vatElement = document.getElementById('vat');
    const totalElement = document.getElementById('total');
    
    if (!servicesList) return;
    
    // Update services list
    if (selectedServices.length === 0) {
        servicesList.innerHTML = '<p class="no-services">Žiadne služby nie sú vybrané</p>';
    } else {
        servicesList.innerHTML = selectedServices.map(service => `
            <div class="service-item-calc">
                <span class="service-name-calc">${service.name}</span>
                <span class="service-price-calc">${service.price.toFixed(2)} EUR</span>
            </div>
        `).join('');
    }
    
    // Calculate totals
    const subtotal = selectedServices.reduce((sum, service) => sum + service.price, 0);
    const discountAmountValue = subtotal * (currentDiscount / 100);
    const subtotalAfterDiscountValue = subtotal - discountAmountValue;
    const vat = subtotalAfterDiscountValue * 0.2;
    const total = subtotalAfterDiscountValue + vat;
    
    // Update display
    if (subtotalElement) subtotalElement.textContent = `${subtotal.toFixed(2)} EUR`;
    if (discountPercent) discountPercent.textContent = currentDiscount;
    if (discountAmount) discountAmount.textContent = `-${discountAmountValue.toFixed(2)} EUR`;
    if (subtotalAfterDiscount) subtotalAfterDiscount.textContent = `${subtotalAfterDiscountValue.toFixed(2)} EUR`;
    if (vatElement) vatElement.textContent = `${vat.toFixed(2)} EUR`;
    if (totalElement) totalElement.textContent = `${total.toFixed(2)} EUR`;
    
    // Show/hide discount row
    if (discountRow) {
        discountRow.style.display = currentDiscount > 0 ? 'flex' : 'none';
    }
}

function generatePDF() {
    // Check if html2pdf is available
    if (typeof window.html2pdf === 'undefined') {
        alert('Chyba: html2pdf knižnica nie je načítaná. Skúste obnoviť stránku.');
        return;
    }
    
    // Get customer data
    const customerName = document.getElementById('customerName')?.value || '';
    const customerPhone = document.getElementById('customerPhone')?.value || '';
    const customerEmail = document.getElementById('customerEmail')?.value || '';
    const customerAddress = document.getElementById('customerAddress')?.value || '';
    const cemetery = document.getElementById('cemetery')?.value || '';
    
    // Validate required fields
    if (!customerName || !customerPhone) {
        alert('Prosím vyplňte meno a telefón zákazníka.');
        return;
    }
    
    if (selectedServices.length === 0) {
        alert('Prosím vyberte aspoň jednu službu.');
        return;
    }
    
    // Calculate totals
    const subtotal = selectedServices.reduce((sum, service) => sum + service.price, 0);
    const discountAmountValue = subtotal * (currentDiscount / 100);
    const subtotalAfterDiscountValue = subtotal - discountAmountValue;
    const vat = subtotalAfterDiscountValue * 0.2;
    const total = subtotalAfterDiscountValue + vat;
    
    // Create PDF content
    const pdfContent = createPDFContent({
        customerName,
        customerPhone,
        customerEmail,
        customerAddress,
        cemetery,
        selectedServices,
        subtotal,
        discountAmountValue,
        subtotalAfterDiscountValue,
        vat,
        total,
        currentDiscount
    });
    
    // Generate PDF
    const opt = {
        margin: 1,
        filename: `cenova-ponuka-${customerName.replace(/\s+/g, '-').toLowerCase()}.pdf`,
        image: { type: 'jpeg', quality: 0.98 },
        html2canvas: { 
            scale: 2,
            useCORS: true,
            letterRendering: true
        },
        jsPDF: { 
            unit: 'in', 
            format: 'a4', 
            orientation: 'portrait',
            compress: true
        }
    };
    
    html2pdf().set(opt).from(pdfContent).save();
}

function createPDFContent(data) {
    const today = new Date().toLocaleDateString('sk-SK');
    
    return `
        <div style="font-family: 'Inter', Arial, sans-serif; max-width: 800px; margin: 0 auto; padding: 20px; color: #1e293b;">
            <!-- Header -->
            <div style="text-align: center; margin-bottom: 30px; border-bottom: 3px solid #5e2e60; padding-bottom: 20px;">
                <h1 style="color: #5e2e60; font-size: 28px; margin: 0; font-weight: 700;">eSpomienka</h1>
                <p style="color: #5f8132; font-size: 16px; margin: 5px 0; font-weight: 500;">Starostlivosť o hrobové miesta</p>
                <h2 style="color: #327881; font-size: 20px; margin: 10px 0;">Cenová ponuka</h2>
            </div>
            
            <!-- Contact and Date -->
            <div style="display: flex; justify-content: space-between; margin-bottom: 30px; font-size: 12px; color: #6b7280;">
                <div>
                    <strong>Kontakt:</strong><br>
                    Vladimír Seman<br>
                    +421 951 553 464<br>
                    <EMAIL>
                </div>
                <div style="text-align: right;">
                    <strong>Dátum:</strong> ${today}
                </div>
            </div>
            
            <!-- Customer Information -->
            <div style="margin-bottom: 30px; background: #f8fafc; padding: 20px; border-radius: 8px; border-left: 4px solid #5e2e60;">
                <h3 style="color: #5e2e60; font-size: 16px; margin: 0 0 15px 0; font-weight: 600;">Údaje o zákazníkovi</h3>
                <div style="font-size: 14px; line-height: 1.6;">
                    <p style="margin: 5px 0;"><strong>Meno:</strong> ${data.customerName}</p>
                    <p style="margin: 5px 0;"><strong>Telefón:</strong> ${data.customerPhone}</p>
                    ${data.customerEmail ? `<p style="margin: 5px 0;"><strong>Email:</strong> ${data.customerEmail}</p>` : ''}
                    ${data.customerAddress ? `<p style="margin: 5px 0;"><strong>Adresa:</strong> ${data.customerAddress}</p>` : ''}
                    ${data.cemetery ? `<p style="margin: 5px 0;"><strong>Cintorín/Lokalita:</strong> ${data.cemetery}</p>` : ''}
                </div>
            </div>
            
            <!-- Services -->
            <div style="margin-bottom: 30px;">
                <h3 style="color: #5e2e60; font-size: 16px; margin: 0 0 15px 0; font-weight: 600;">Vybrané služby</h3>
                <table style="width: 100%; border-collapse: collapse; font-size: 14px;">
                    <thead>
                        <tr style="background: #f3f4f6;">
                            <th style="padding: 12px; text-align: left; border: 1px solid #e5e7eb; font-weight: 600;">Služba</th>
                            <th style="padding: 12px; text-align: right; border: 1px solid #e5e7eb; font-weight: 600;">Cena</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${data.selectedServices.map(service => `
                            <tr>
                                <td style="padding: 12px; border: 1px solid #e5e7eb;">${service.name}</td>
                                <td style="padding: 12px; text-align: right; border: 1px solid #e5e7eb; color: #5f8132; font-weight: 500;">${service.price.toFixed(2)} EUR</td>
                            </tr>
                        `).join('')}
                    </tbody>
                </table>
            </div>
            
            <!-- Price Summary -->
            <div style="margin-bottom: 30px; background: #f8fafc; padding: 20px; border-radius: 8px;">
                <h3 style="color: #5e2e60; font-size: 16px; margin: 0 0 15px 0; font-weight: 600;">Súhrn cien</h3>
                <table style="width: 100%; font-size: 14px;">
                    <tr>
                        <td style="padding: 8px 0; border-bottom: 1px solid #e5e7eb;">Súčet bez DPH:</td>
                        <td style="padding: 8px 0; text-align: right; border-bottom: 1px solid #e5e7eb; font-weight: 500;">${data.subtotal.toFixed(2)} EUR</td>
                    </tr>
                    ${data.currentDiscount > 0 ? `
                        <tr>
                            <td style="padding: 8px 0; border-bottom: 1px solid #e5e7eb; color: #ef4444;">Zľava (${data.currentDiscount}%):</td>
                            <td style="padding: 8px 0; text-align: right; border-bottom: 1px solid #e5e7eb; color: #ef4444; font-weight: 500;">-${data.discountAmountValue.toFixed(2)} EUR</td>
                        </tr>
                        <tr>
                            <td style="padding: 8px 0; border-bottom: 1px solid #e5e7eb;">Súčet po zľave:</td>
                            <td style="padding: 8px 0; text-align: right; border-bottom: 1px solid #e5e7eb; font-weight: 500;">${data.subtotalAfterDiscountValue.toFixed(2)} EUR</td>
                        </tr>
                    ` : ''}
                    <tr>
                        <td style="padding: 8px 0; border-bottom: 1px solid #e5e7eb;">DPH 20%:</td>
                        <td style="padding: 8px 0; text-align: right; border-bottom: 1px solid #e5e7eb; font-weight: 500;">${data.vat.toFixed(2)} EUR</td>
                    </tr>
                    <tr style="font-size: 16px; font-weight: 700; color: #5e2e60;">
                        <td style="padding: 15px 0; border-top: 2px solid #5e2e60;">Celkom s DPH:</td>
                        <td style="padding: 15px 0; text-align: right; border-top: 2px solid #5e2e60;">${data.total.toFixed(2)} EUR</td>
                    </tr>
                </table>
            </div>
            
            <!-- Footer -->
            <div style="text-align: center; font-size: 12px; color: #6b7280; border-top: 1px solid #e5e7eb; padding-top: 20px;">
                <p style="margin: 5px 0;">Ďakujeme za Váš záujem o naše služby!</p>
                <p style="margin: 5px 0;">Pre viac informácií nás kontaktujte na +421 951 553 464 alebo <EMAIL></p>
                <p style="margin: 5px 0; font-style: italic;">eSpomienka - Starostlivosť o hrobové miesta</p>
            </div>
        </div>
    `;
}

// Modal functions
function showModal(modalId) {
    const modal = document.getElementById(modalId);
    if (modal) {
        modal.classList.add('active');
    }
}

function closeModal(modalId) {
    const modal = document.getElementById(modalId);
    if (modal) {
        modal.classList.remove('active');
    }
}

// Close modal when clicking outside
document.addEventListener('click', function(e) {
    if (e.target.classList.contains('modal')) {
        e.target.classList.remove('active');
    }
});

// Placeholder functions for other features
function showAddClientModal() {
    showModal('addClientModal');
}

function showAddOrderModal() {
    showModal('addOrderModal');
}

function showOrdersListModal() {
    showModal('ordersListModal');
}

function showCalendarView() {
    showModal('calendarModal');
}

function createOrderFromQuote() {
    alert('Funkcia vytvorenia objednávky z ponuky bude implementovaná neskôr.');
}

function exportClients() {
    alert('Funkcia exportu klientov bude implementovaná neskôr.');
}
