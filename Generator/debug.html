<!DOCTYPE html>
<html lang="sk">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug PDF Generator</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/html2pdf.js/0.10.1/html2pdf.bundle.min.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .btn {
            background: #5e2e60;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 0;
        }
        .btn:hover {
            background: #7c3aed;
        }
        .form-group {
            margin-bottom: 15px;
        }
        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        .form-group input {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        .error {
            color: red;
            margin: 10px 0;
        }
        .success {
            color: green;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Debug PDF Generator</h1>
        
        <div class="form-group">
            <label>Meno zákazníka:</label>
            <input type="text" id="customerName" value="Ján Novák" placeholder="Zadajte meno">
        </div>
        
        <div class="form-group">
            <label>Telefón:</label>
            <input type="text" id="customerPhone" value="+421 901 234 567" placeholder="Zadajte telefón">
        </div>
        
        <div class="form-group">
            <label>Email:</label>
            <input type="text" id="customerEmail" value="<EMAIL>" placeholder="Zadajte email">
        </div>
        
        <button class="btn" onclick="testPDF()">Test PDF generátor</button>
        <button class="btn" onclick="checkLibrary()">Skontrolovať knižnicu</button>
        
        <div id="status"></div>
    </div>

    <script>
        function checkLibrary() {
            const status = document.getElementById('status');
            if (typeof window.html2pdf === 'undefined') {
                status.innerHTML = '<div class="error">❌ html2pdf knižnica nie je načítaná!</div>';
            } else {
                status.innerHTML = '<div class="success">✅ html2pdf knižnica je načítaná správne</div>';
            }
        }
        
        function testPDF() {
            const status = document.getElementById('status');
            
            // Check if html2pdf is available
            if (typeof window.html2pdf === 'undefined') {
                status.innerHTML = '<div class="error">❌ html2pdf knižnica nie je načítaná!</div>';
                return;
            }
            
            // Get form data
            const customerName = document.getElementById('customerName').value;
            const customerPhone = document.getElementById('customerPhone').value;
            const customerEmail = document.getElementById('customerEmail').value;
            
            if (!customerName || !customerPhone) {
                status.innerHTML = '<div class="error">❌ Prosím vyplňte meno a telefón!</div>';
                return;
            }
            
            status.innerHTML = '<div>🔄 Generujem PDF...</div>';
            
            // Create simple PDF content
            const pdfContent = `
                <div style="font-family: Arial, sans-serif; max-width: 800px; margin: 0 auto; padding: 30px; color: #1e293b; background: white;">
                    <div style="text-align: center; margin-bottom: 40px; border-bottom: 3px solid #5e2e60; padding-bottom: 25px;">
                        <h1 style="color: #5e2e60; font-size: 32px; margin: 0; font-weight: 700;">eHroby</h1>
                        <p style="color: #5f8132; font-size: 16px; margin: 5px 0;">vytvárame pokojné spomienky</p>
                        <h2 style="color: #327881; font-size: 24px; margin: 10px 0;">Cenová ponuka</h2>
                    </div>
                    
                    <div style="margin-bottom: 35px; background: #f8fafc; padding: 20px; border-radius: 12px;">
                        <h3 style="color: #5e2e60; font-size: 18px; margin: 0 0 15px 0;">Údaje o zákazníkovi</h3>
                        <p><strong>Meno:</strong> ${customerName}</p>
                        <p><strong>Telefón:</strong> ${customerPhone}</p>
                        <p><strong>Email:</strong> ${customerEmail}</p>
                    </div>
                    
                    <div style="margin-bottom: 35px;">
                        <h3 style="color: #5e2e60; font-size: 18px; margin: 0 0 20px 0;">Testové služby</h3>
                        <table style="width: 100%; border-collapse: collapse; font-size: 14px;">
                            <tr style="background: #5e2e60; color: white;">
                                <th style="padding: 15px; text-align: left;">Služba</th>
                                <th style="padding: 15px; text-align: right;">Cena</th>
                            </tr>
                            <tr style="background: #ffffff;">
                                <td style="padding: 15px; border-bottom: 1px solid #e5e7eb;">Jednorazové umytie</td>
                                <td style="padding: 15px; text-align: right; border-bottom: 1px solid #e5e7eb; color: #5f8132; font-weight: 600;">59,99 €</td>
                            </tr>
                        </table>
                    </div>
                    
                    <div style="text-align: center; margin-top: 40px; padding-top: 25px; border-top: 2px solid #e5e7eb;">
                        <p style="color: #5e2e60; font-weight: 600;">Ďakujeme za Váš záujem!</p>
                        <p style="color: #6b7280;">+421 951 553 464 | <EMAIL></p>
                    </div>
                </div>
            `;
            
            // Generate PDF
            const opt = {
                margin: 0.5,
                filename: 'test-cenova-ponuka.pdf',
                image: { type: 'jpeg', quality: 0.98 },
                html2canvas: { 
                    scale: 2,
                    useCORS: true,
                    letterRendering: true,
                    allowTaint: true
                },
                jsPDF: { 
                    unit: 'in', 
                    format: 'a4', 
                    orientation: 'portrait',
                    compress: true
                }
            };
            
            html2pdf().set(opt).from(pdfContent).save()
                .then(() => {
                    status.innerHTML = '<div class="success">✅ PDF bolo úspešne vygenerované!</div>';
                })
                .catch((error) => {
                    status.innerHTML = '<div class="error">❌ Chyba pri generovaní PDF: ' + error.message + '</div>';
                    console.error('PDF Error:', error);
                });
        }
        
        // Auto-check library on load
        window.addEventListener('load', checkLibrary);
    </script>
</body>
</html>
