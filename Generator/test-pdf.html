<!DOCTYPE html>
<html lang="sk">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test PDF - eHroby</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/html2pdf.js/0.10.1/html2pdf.bundle.min.js"></script>
    <style>
        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .test-content {
            max-width: 800px;
            margin: 0 auto;
            padding: 30px;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .btn {
            background: #5e2e60;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px;
        }
        .btn:hover {
            background: #7c3aed;
        }
    </style>
</head>
<body>
    <div class="test-content">
        <h1>Test PDF generátora</h1>
        <p>Tento test overuje správne fungovanie slovenských znakov v PDF:</p>
        
        <h2>Slovenské znaky:</h2>
        <ul>
            <li>á, é, í, ó, ú, ý</li>
            <li>ä, ô</li>
            <li>č, ď, ľ, ň, š, ť, ž</li>
            <li>Á, É, Í, Ó, Ú, Ý</li>
            <li>Ä, Ô</li>
            <li>Č, Ď, Ľ, Ň, Š, Ť, Ž</li>
        </ul>
        
        <h2>Testové vety:</h2>
        <p>Ďakujeme za Váš záujem o naše služby!</p>
        <p>Starostlivosť o hrobové miesta</p>
        <p>Cenová ponuka</p>
        <p>Údaje o zákazníkovi</p>
        <p>Vybrané služby</p>
        <p>Súhrn cien</p>
        <p>Poznámky</p>
        
        <h2>Čísla a ceny:</h2>
        <p>Cena: 59,99 €</p>
        <p>DPH 20%: 11,99 €</p>
        <p>Celkom s DPH: 71,99 €</p>
        
        <button class="btn" onclick="generateTestPDF()">Generovať test PDF</button>
    </div>

    <script>
        function generateTestPDF() {
            const element = document.querySelector('.test-content');
            
            const opt = {
                margin: 0.5,
                filename: 'test-slovensky-pdf.pdf',
                image: { type: 'jpeg', quality: 0.98 },
                html2canvas: { 
                    scale: 2,
                    useCORS: true,
                    letterRendering: true,
                    allowTaint: true
                },
                jsPDF: { 
                    unit: 'in', 
                    format: 'a4', 
                    orientation: 'portrait',
                    compress: true
                }
            };
            
            html2pdf().set(opt).from(element).save();
        }
    </script>
</body>
</html>
