<!DOCTYPE html>
<html lang="sk">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>eHroby - <PERSON><PERSON><PERSON><PERSON> cenov<PERSON>ch ponúk</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/html2pdf.js/0.10.1/html2pdf.bundle.min.js"></script>
    <link rel="stylesheet" href="styles.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="app">
        <header class="header">
            <div class="container">
                <h1><i class="fas fa-file-invoice"></i> eHroby - <PERSON>r<PERSON><PERSON> cenov<PERSON>ch ponúk</h1>
                <p>Profesionálne služby starostlivosti o hrobové miesta</p>
            </div>
        </header>

        <main class="main">
            <div class="container">
                <!-- Client Data Section -->
                <div class="form-section">
                    <h2><i class="fas fa-user"></i> Údaje klienta</h2>
                    <div class="form-grid">
                        <div class="form-group">
                            <label>Meno a priezvisko *</label>
                            <input type="text" id="clientName" placeholder="Zadajte meno a priezvisko">
                        </div>
                        <div class="form-group">
                            <label>Telefón *</label>
                            <input type="tel" id="clientPhone" placeholder="+421 xxx xxx xxx">
                        </div>
                        <div class="form-group">
                            <label>Email</label>
                            <input type="email" id="clientEmail" placeholder="<EMAIL>">
                        </div>
                        <div class="form-group">
                            <label>Adresa hrobových miest</label>
                            <input type="text" id="clientAddress" placeholder="Cintorín, mesto">
                        </div>
                    </div>
                </div>

                <!-- Services Section -->
                <div class="form-section">
                    <h2><i class="fas fa-cross"></i> Výber služieb</h2>
                    
                    <div class="service-item">
                        <label class="checkbox-item">
                            <input type="checkbox" id="service1" data-price="59.99">
                            <span class="checkmark"></span>
                            <span class="service-name">Jednorazové umytie (1× ročne)</span>
                            <span class="service-price">59,99 €</span>
                        </label>
                    </div>
                    
                    <div class="service-item">
                        <label class="checkbox-item">
                            <input type="checkbox" id="service2" data-price="113.99">
                            <span class="checkmark"></span>
                            <span class="service-name">DUO umytie (2× ročne)</span>
                            <span class="service-price">113,99 €</span>
                        </label>
                    </div>
                    
                    <div class="service-item">
                        <label class="checkbox-item">
                            <input type="checkbox" id="service3" data-price="227.99">
                            <span class="checkmark"></span>
                            <span class="service-name">Štvrťročná starostlivosť (4× ročne)</span>
                            <span class="service-price">227,99 €</span>
                        </label>
                    </div>
                </div>

                <!-- Summary Section -->
                <div class="summary-section">
                    <div class="total-price">
                        <h2 id="totalPrice">Celková suma: 0,00 €</h2>
                        <p>Cena s DPH (20%)</p>
                    </div>

                    <button class="btn-generate" onclick="generatePDF()">
                        <i class="fas fa-file-pdf"></i> Generovať PDF ponuku
                    </button>
                </div>
            </div>
        </main>

        <footer class="footer">
            <div class="container">
                <p>&copy; 2024 eHroby - Vladimír Seman | +421 951 553 464 | <EMAIL></p>
            </div>
        </footer>
    </div>

    <script>
        // Update total price when services change
        function updateTotal() {
            let total = 0;
            document.querySelectorAll('input[type="checkbox"]:checked').forEach(checkbox => {
                total += parseFloat(checkbox.getAttribute('data-price'));
            });
            document.getElementById('totalPrice').textContent = `Celková suma: ${total.toFixed(2)} €`;
        }

        // Add event listeners to checkboxes
        document.querySelectorAll('input[type="checkbox"]').forEach(checkbox => {
            checkbox.addEventListener('change', updateTotal);
        });

        function generatePDF() {
            console.log('generatePDF called');
            
            // Check if html2pdf is available
            if (typeof window.html2pdf === 'undefined') {
                alert('Chyba: html2pdf knižnica nie je načítaná. Skúste obnoviť stránku.');
                return;
            }

            // Get form data
            const clientName = document.getElementById('clientName').value;
            const clientPhone = document.getElementById('clientPhone').value;
            const clientEmail = document.getElementById('clientEmail').value;
            const clientAddress = document.getElementById('clientAddress').value;

            // Validate required fields
            if (!clientName || !clientPhone) {
                alert('Prosím vyplňte meno a telefón zákazníka.');
                return;
            }

            // Get selected services
            const selectedServices = [];
            document.querySelectorAll('input[type="checkbox"]:checked').forEach(checkbox => {
                const serviceItem = checkbox.closest('.service-item');
                const serviceName = serviceItem.querySelector('.service-name').textContent;
                const servicePrice = parseFloat(checkbox.getAttribute('data-price'));
                selectedServices.push({ name: serviceName, price: servicePrice });
            });

            if (selectedServices.length === 0) {
                alert('Prosím vyberte aspoň jednu službu.');
                return;
            }

            // Calculate total
            const total = selectedServices.reduce((sum, service) => sum + service.price, 0);
            const bezDph = total / 1.2;
            const dph = total - bezDph;

            // Create PDF content
            const today = new Date().toLocaleDateString('sk-SK');
            const pdfContent = `
                <div style="font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; max-width: 800px; margin: 0 auto; padding: 30px; color: #1e293b; background: white;">
                    <!-- Header -->
                    <div style="display: flex; align-items: center; margin-bottom: 40px; border-bottom: 3px solid #5e2e60; padding-bottom: 25px;">
                        <img src="https://storage.googleapis.com/espomienka/logo36.png" alt="eHroby Logo" style="width: 60px; height: 32px; margin-right: 20px;" onerror="this.style.display='none'">
                        <div style="flex: 1;">
                            <h1 style="color: #5e2e60; font-size: 32px; margin: 0; font-weight: 700;">eHroby</h1>
                            <p style="color: #5f8132; font-size: 16px; margin: 5px 0;">vytvárame pokojné spomienky</p>
                        </div>
                        <div style="text-align: right;">
                            <h2 style="color: #327881; font-size: 24px; margin: 0;">Cenová ponuka</h2>
                            <p style="color: #6b7280; font-size: 14px; margin: 5px 0;">Dátum: ${today}</p>
                        </div>
                    </div>
                    
                    <!-- Customer Info -->
                    <div style="margin-bottom: 35px; background: #f8fafc; padding: 20px; border-radius: 12px; border-left: 4px solid #5e2e60;">
                        <h3 style="color: #5e2e60; font-size: 18px; margin: 0 0 15px 0;">Údaje o zákazníkovi</h3>
                        <p><strong>Meno:</strong> ${clientName}</p>
                        <p><strong>Telefón:</strong> ${clientPhone}</p>
                        ${clientEmail ? `<p><strong>Email:</strong> ${clientEmail}</p>` : ''}
                        ${clientAddress ? `<p><strong>Adresa:</strong> ${clientAddress}</p>` : ''}
                    </div>
                    
                    <!-- Services -->
                    <div style="margin-bottom: 35px;">
                        <h3 style="color: #5e2e60; font-size: 18px; margin: 0 0 20px 0;">Vybrané služby</h3>
                        <table style="width: 100%; border-collapse: collapse; font-size: 14px;">
                            <thead>
                                <tr style="background: linear-gradient(135deg, #5e2e60, #7c3aed);">
                                    <th style="padding: 15px; text-align: left; color: white;">Služba</th>
                                    <th style="padding: 15px; text-align: right; color: white;">Cena</th>
                                </tr>
                            </thead>
                            <tbody>
                                ${selectedServices.map((service, index) => `
                                    <tr style="background: ${index % 2 === 0 ? '#ffffff' : '#f8fafc'};">
                                        <td style="padding: 15px; border-bottom: 1px solid #e5e7eb;">${service.name}</td>
                                        <td style="padding: 15px; text-align: right; border-bottom: 1px solid #e5e7eb; color: #5f8132; font-weight: 600;">${service.price.toFixed(2)} €</td>
                                    </tr>
                                `).join('')}
                            </tbody>
                        </table>
                    </div>
                    
                    <!-- Price Summary -->
                    <div style="margin-bottom: 35px; background: #f8fafc; padding: 25px; border-radius: 12px;">
                        <h3 style="color: #5e2e60; font-size: 18px; margin: 0 0 20px 0;">Súhrn cien</h3>
                        <table style="width: 100%; font-size: 15px;">
                            <tr>
                                <td style="padding: 8px 0;">Základ dane:</td>
                                <td style="text-align: right; font-weight: 500;">${bezDph.toFixed(2)} €</td>
                            </tr>
                            <tr>
                                <td style="padding: 8px 0;">DPH 20%:</td>
                                <td style="text-align: right; font-weight: 500;">${dph.toFixed(2)} €</td>
                            </tr>
                            <tr style="font-size: 20px; font-weight: 700; color: #5e2e60;">
                                <td style="padding: 20px 0; border-top: 3px solid #5e2e60;">Celkom s DPH:</td>
                                <td style="text-align: right; border-top: 3px solid #5e2e60;">${total.toFixed(2)} €</td>
                            </tr>
                        </table>
                    </div>
                    
                    <!-- Footer -->
                    <div style="text-align: center; margin-top: 40px; padding-top: 25px; border-top: 2px solid #e5e7eb;">
                        <p style="color: #5e2e60; font-weight: 600;">🙏 Ďakujeme za Váš záujem o naše služby!</p>
                        <p style="color: #6b7280;">Pre viac informácií nás kontaktujte na <strong>+421 951 553 464</strong> alebo <strong><EMAIL></strong></p>
                        <p style="color: #5e2e60; font-weight: 500;">eHroby - vytvárame pokojné spomienky</p>
                    </div>
                </div>
            `;

            // Generate PDF
            const opt = {
                margin: 0.5,
                filename: `cenova-ponuka-ehroby-${clientName.replace(/\s+/g, '-').toLowerCase()}.pdf`,
                image: { type: 'jpeg', quality: 0.98 },
                html2canvas: { 
                    scale: 2,
                    useCORS: true,
                    letterRendering: true,
                    allowTaint: true
                },
                jsPDF: { 
                    unit: 'in', 
                    format: 'a4', 
                    orientation: 'portrait',
                    compress: true
                }
            };

            html2pdf().set(opt).from(pdfContent).save()
                .then(() => {
                    console.log('PDF generated successfully');
                    alert('PDF bolo úspešne vygenerované!');
                })
                .catch((error) => {
                    console.error('PDF generation error:', error);
                    alert('Chyba pri generovaní PDF: ' + error.message);
                });
        }
    </script>
</body>
</html>
